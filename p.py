# ===================================================================
# 最终修正版分析脚本 (使用标准THD计算方法)
# ===================================================================
import numpy as np
import pandas as pd
import scipy.io
import pywt

# -------------------------------------------------------------------
# 1. 配置参数 (所有可调参数都在这里，方便修改)
# -------------------------------------------------------------------
class Config:
    """存放所有配置参数的类"""
    MAT_FILE_PATH = 'pv_fault_data.mat'
    SIM_TIME = 3.0
    FAULT_TIME = 1.0
    GRID_FREQ = 50.0
    CYCLES_PER_WINDOW = 2
    WINDOW_OVERLAP = 0.5
    WAVELET_TYPE = 'db4'
    WAVELET_LEVEL = 4
    POST_FAULT_STABLE_OFFSET = 5

# -------------------------------------------------------------------
# 2. 数据加载与准备 (与之前版本相同)
# -------------------------------------------------------------------
def load_and_prepare_data(config):
    """从.mat文件加载数据并进行初步处理。"""
    print("--- 步骤1: 加载和准备数据 ---")
    try:
        mat_data = scipy.io.loadmat(config.MAT_FILE_PATH)
        print(f"✅ 文件 '{config.MAT_FILE_PATH}' 加载成功!")
        print(f"   文件中包含的变量: {list(mat_data.keys())}")
    except FileNotFoundError:
        print(f"❌ 错误: 未找到文件 '{config.MAT_FILE_PATH}'。")
        return None, None

    required_vars = ['Ia', 'Ib', 'Ic', 'Va', 'Vb', 'Vc', 'Vdc']
    data_dict = {}
    for var in required_vars:
        if var in mat_data:
            data_dict[var] = mat_data[var].flatten()
        else:
            print(f"⚠️ 警告: 在.mat文件中未找到变量 '{var}'，将跳过。")
            
    if 'Ia' not in data_dict:
        print("❌ 错误: 缺少关键数据 'Ia'，程序无法继续。")
        return None, None

    num_samples = len(data_dict['Ia'])
    sampling_rate = num_samples / config.SIM_TIME
    print(f"   - 仿真参数: 总时长={config.SIM_TIME}s, 采样点={num_samples}, 采样率={sampling_rate:.2f}Hz")
    
    return data_dict, sampling_rate

# -------------------------------------------------------------------
# 3. 特征提取函数 (已修正和重构)
# -------------------------------------------------------------------
def extract_all_features_for_window(window_data, fs, config):
    """
    为一个窗口的数据提取所有指定的特征。
    此版本使用了标准的THD计算方法。
    """
    features = {}
    ia_win = window_data['Ia']

    # --- A. 计算总RMS值 ---
    # 这是计算标准THD的第一步
    total_rms = np.sqrt(np.mean(ia_win**2))
    features['ia_total_rms'] = total_rms

    # --- B. 精确提取基波幅值和计算标准THD ---
    N = len(ia_win)
    yf = np.fft.fft(ia_win)

    # 查找基波频率对应的精确峰值
    # 为避免单点误差，在基波频率附近的一个小范围内搜索最大值
    freq_bins = np.fft.fftfreq(N, 1/fs) # 获取每个FFT点的频率
    freq_bins = freq_bins[:N//2]       # 取正频率部分

    # 计算频率分辨率
    freq_resolution = fs / N

    # 动态调整搜索范围，确保至少有3个频率点
    search_range = max(5, 3 * freq_resolution) # 在基波频率附近搜索
    min_freq_idx = np.argmin(np.abs(freq_bins - (config.GRID_FREQ - search_range)))
    max_freq_idx = np.argmin(np.abs(freq_bins - (config.GRID_FREQ + search_range)))

    # 确保搜索范围有效
    if max_freq_idx <= min_freq_idx:
        # 如果范围无效，直接使用最接近基波频率的点
        fundamental_peak_idx = np.argmin(np.abs(freq_bins - config.GRID_FREQ))
    else:
        # 找到该范围内的峰值索引和幅值
        fundamental_peak_idx = min_freq_idx + np.argmax(np.abs(yf[min_freq_idx:max_freq_idx]))

    fundamental_mag_peak = 2.0/N * np.abs(yf[fundamental_peak_idx])
    
    # 将FFT峰值幅值转换为RMS值 (峰值 / sqrt(2))
    fundamental_rms = fundamental_mag_peak / np.sqrt(2)
    features['ia_fundamental_rms'] = fundamental_rms

    # --- C. 计算标准THD ---
    # THD = sqrt(总RMS^2 - 基波RMS^2) / 基波RMS
    if fundamental_rms > 0:
        # 确保total_rms^2 >= fundamental_rms^2, 避免负数开方
        non_fundamental_rms_sq = total_rms**2 - fundamental_rms**2
        if non_fundamental_rms_sq < 0: non_fundamental_rms_sq = 0 # 处理浮点误差
        
        thd = np.sqrt(non_fundamental_rms_sq) / fundamental_rms
    else:
        thd = 0 # 基波为0时，THD无意义
    features['ia_thd_standard'] = thd

    # --- D. 其他时域、小波、直流和功率特征 (保持不变) ---
    features['ia_peak'] = np.max(np.abs(ia_win))
    coeffs = pywt.wavedec(ia_win, config.WAVELET_TYPE, level=config.WAVELET_LEVEL)
    for i in range(config.WAVELET_LEVEL):
        features[f'ia_wavelet_energy_d{i+1}'] = np.sum(coeffs[i+1]**2)

    features['dc_volt_mean'] = np.mean(window_data['Vdc'])
    features['dc_volt_std'] = np.std(window_data['Vdc'])

    p_inst = (window_data['Va'] * window_data['Ia'] +
              window_data['Vb'] * window_data['Ib'] +
              window_data['Vc'] * window_data['Ic'])
    features['active_power_P'] = np.mean(p_inst)
    
    return features

# -------------------------------------------------------------------
# 4. 主处理流程 (与之前版本相同，但会调用新的特征提取函数)
# -------------------------------------------------------------------
def main():
    """主函数，执行整个分析流程"""
    config = Config()
    data_dict, sampling_rate = load_and_prepare_data(config)
    if data_dict is None: return

    print("\n--- 步骤2: 数据分窗 ---")
    window_size = int(sampling_rate * config.CYCLES_PER_WINDOW / config.GRID_FREQ)
    step_size = int(window_size * (1 - config.WINDOW_OVERLAP))
    num_windows = (len(data_dict['Ia']) - window_size) // step_size + 1
    print(f"   - 将生成 {num_windows} 个分析窗口。")

    print("\n--- 步骤3: 循环提取特征 ---")
    all_features_list = []
    for i in range(num_windows):
        start_idx, end_idx = i * step_size, i * step_size + window_size
        current_window_data = {key: data[start_idx:end_idx] for key, data in data_dict.items()}
        features = extract_all_features_for_window(current_window_data, sampling_rate, config)
        all_features_list.append(features)
    feature_df = pd.DataFrame(all_features_list)
    print("✅ 特征提取完成。")

    print("\n--- 步骤4: 特征标注 ---")
    fault_sample_index = int(config.FAULT_TIME * sampling_rate)
    fault_window_index = fault_sample_index // step_size
    feature_df['label'] = 0
    feature_df.loc[fault_window_index:, 'label'] = 1
    print("✅ 数据标注完成。")

    print("\n--- 步骤5: 计算修正后的故障影响向量 ---")
    pre_fault_idx = fault_window_index - 1
    post_fault_idx = fault_window_index + config.POST_FAULT_STABLE_OFFSET
    if pre_fault_idx < 0 or post_fault_idx >= len(feature_df):
        print("❌ 错误: 窗口数量不足。")
        return

    print(f"   - 故障前稳态窗口索引: {pre_fault_idx}")
    print(f"   - 故障后稳态窗口索引: {post_fault_idx}\n")
    
    pre_fault_features = feature_df.loc[pre_fault_idx]
    post_fault_features = feature_df.loc[post_fault_idx]
    impact_vector = post_fault_features.drop('label') - pre_fault_features.drop('label')
    impact_df = pd.DataFrame({
        'Pre-Fault Value': pre_fault_features.drop('label'),
        'Post-Fault Value': post_fault_features.drop('label'),
        'Impact (Delta)': impact_vector
    })
    
    pd.set_option('display.float_format', '{:.6f}'.format)
    print("--- 修正后的故障影响特征向量 ---")
    print(impact_df)

# -------------------------------------------------------------------
# 5. 运行主程序
# -------------------------------------------------------------------
if __name__ == '__main__':
    main()