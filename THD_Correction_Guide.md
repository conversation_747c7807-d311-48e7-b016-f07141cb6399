# THD计算修正指南 - 按照MATLAB标准

## 🎯 问题描述

您上传的THD图片显示，我们原有的THD计算结果与MATLAB的计算结果不匹配。主要问题包括：

1. **THD数值偏差**: 计算出的THD值与MATLAB结果存在差异
2. **变化趋势不符**: 故障前后的THD变化趋势可能与预期不一致
3. **谐波检测精度**: 谐波峰值检测可能不够精确

## 📚 MATLAB THD标准

根据MATLAB官方文档 ([THD函数参考](https://ww2.mathworks.cn/help/releases/R2024b/sps/powersys/ref/thd.html))，THD的标准计算方法为：

### 数学公式
```
THD = sqrt(sum(H2² + H3² + ... + Hn²)) / H1 × 100%
```

其中：
- `H1`: 基波幅值
- `H2, H3, ..., Hn`: 各次谐波幅值
- `n`: 最高谐波次数（通常到50次或奈奎斯特频率）

### 关键要点
1. **不使用窗函数**: THD计算需要保持信号的真实幅值关系
2. **扩展谐波范围**: 考虑2-50次谐波（或到奈奎斯特频率）
3. **精确峰值检测**: 在±0.5Hz范围内搜索谐波峰值
4. **标准化幅值**: 使用标准的单边谱幅值公式

## 🔧 主要修正内容

### 1. 移除窗函数
**原有代码**:
```python
# 应用汉宁窗减少频谱泄漏
windowed_signal = signal_data * np.hanning(N)
yf = np.fft.fft(windowed_signal)
```

**修正后代码**:
```python
# 按照MATLAB标准，不使用窗函数进行THD计算
# 因为THD计算需要保持信号的真实幅值关系
yf = np.fft.fft(signal_data)
```

### 2. 扩展谐波检测范围
**原有代码**:
```python
for h in config.HARMONICS:  # 只检测配置的几个谐波
    # ...
```

**修正后代码**:
```python
# 扩展谐波范围，按照MATLAB标准
max_harmonic = min(50, int((fs/2) / fundamental_freq))
for h in range(2, max_harmonic + 1):  # 从2次谐波开始到50次
    # ...
```

### 3. 精确的谐波峰值检测
**原有代码**:
```python
# ±1Hz搜索范围
h_search_range = max(1, int(1 / freq_resolution))
```

**修正后代码**:
```python
# ±0.5Hz搜索范围，更精确
h_search_range = max(1, int(0.5 / freq_resolution))
```

### 4. 标准化幅值计算
**原有代码**:
```python
yf_mag = 2.0/N * np.abs(yf[:N//2])
```

**修正后代码**:
```python
yf_mag = np.abs(yf[:N//2]) * 2.0 / N  # 单边谱幅值
yf_mag[0] = yf_mag[0] / 2  # 直流分量不需要乘2
```

## 📊 验证方法

### 1. 理论验证
使用已知的测试信号验证THD计算：
```python
# 基波50Hz，幅值1 + 3次谐波0.1 + 5次谐波0.05
# 理论THD = sqrt(0.1² + 0.05²) / 1.0 × 100% = 11.18%
```

### 2. 实际数据验证
- 加载光伏故障数据
- 分别计算故障前后的THD
- 验证变化趋势是否符合物理预期

## 🚀 使用新的THD计算

### 方法1: 使用独立的修正函数
```python
from matlab_thd_correction import extract_matlab_standard_thd

# 计算THD
result = extract_matlab_standard_thd(signal_data, sampling_rate)
if result:
    print(f"THD: {result['thd_percent']:.2f}%")
```

### 方法2: 使用修正版Jupyter笔记本
1. 打开 `thd_correction_notebook.ipynb`
2. 运行所有单元格
3. 查看测试结果和实际数据分析

### 方法3: 集成到现有代码
将修正后的 `extract_matlab_standard_thd` 函数替换原有的THD计算函数。

## 📈 预期改进效果

### 1. 数值精度提升
- THD计算结果与MATLAB更加一致
- 减少计算误差，提高可信度

### 2. 物理意义正确
- 故障后THD应该增加（谐波增多）
- 变化趋势符合电力系统理论

### 3. 谐波分析更全面
- 检测更多次谐波（2-50次）
- 提供奇偶谐波分量分析
- 更详细的频谱质量指标

## 🔍 对比分析

### 修正前 vs 修正后

| 项目 | 修正前 | 修正后 |
|------|--------|--------|
| 窗函数 | 使用汉宁窗 | 不使用窗函数 |
| 谐波范围 | 3,5,7,11,13,15,17,19次 | 2-50次（完整范围） |
| 峰值检测 | ±1Hz搜索 | ±0.5Hz精确搜索 |
| 幅值计算 | 基础FFT | 标准单边谱 |
| MATLAB一致性 | 可能存在偏差 | 严格按照标准 |

### 预期THD变化
- **故障前**: 约2-3%（正常运行状态）
- **故障后**: 约3-5%（谐波增加）
- **变化趋势**: 明显增加，符合物理预期

## 📝 实施建议

### 立即行动
1. **运行测试**: 先运行 `thd_correction_notebook.ipynb` 验证修正效果
2. **对比结果**: 将新的THD结果与您的MATLAB结果对比
3. **确认改进**: 验证数值精度和变化趋势

### 后续集成
1. **替换函数**: 将修正后的THD计算函数集成到主分析代码中
2. **更新报告**: 重新生成分析报告，使用修正后的THD数据
3. **验证影响**: 检查综合影响因子等依赖THD的指标

### 质量保证
1. **多组数据验证**: 使用不同的数据集验证修正效果
2. **专家评审**: 请电力系统专家评审THD计算的正确性
3. **文档更新**: 更新技术文档，说明THD计算的改进

## 🎯 总结

通过按照MATLAB官方标准修正THD计算逻辑，我们可以：

1. **提高精度**: 获得与MATLAB一致的THD计算结果
2. **确保正确性**: 保证物理意义和变化趋势的正确性
3. **增强可信度**: 提升分析结果的学术和工程价值
4. **标准化**: 建立符合国际标准的THD计算方法

这个修正将显著提升您的光伏故障分析系统的准确性和可靠性！

---

**文件说明**:
- `matlab_thd_correction.py`: 独立的MATLAB标准THD计算函数
- `thd_correction_notebook.ipynb`: 完整的THD修正验证笔记本
- `THD_Correction_Guide.md`: 本修正指南文档

**下一步**: 请运行测试笔记本，验证修正效果，然后我们可以将修正后的函数集成到主分析系统中。
