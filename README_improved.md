# 光伏断路故障电能质量影响特征提取 - 改进版

## 🚀 项目概述

本项目是光伏故障分析系统的改进版本，针对400KW并网光伏发电系统中PV Array4断路故障对电网电能质量的影响进行深度分析，为MPC补偿算法开发提供精确的特征数据和量化指标。

## ✨ 四项核心改进

### 1. 📁 文件组织优化
- **统一输出管理**: 创建规范的文件夹结构
- **分类存储**: 图表、数据、报告分别存放
- **便于管理**: 所有输出文件井然有序

```
results/
├── plots/          # 所有分析图表
├── data/           # CSV数据文件
└── reports/        # 分析报告
```

### 2. 🔧 THD计算逻辑修正
- **汉宁窗处理**: 减少频谱泄漏，提高精度
- **精确峰值检测**: 基波和谐波的准确识别
- **物理意义验证**: 确保故障后THD增加的合理性
- **计算公式优化**: THD = √(∑谐波²) / 基波

### 3. 📊 数据可视化改进
- **图表拆分**: 原概览图分解为多个独立图表
- **重合问题解决**: 
  - 三相电压使用不同线型（实线、虚线、点线）
  - 调整透明度和颜色区分
  - 添加详细统计信息标注
- **可读性提升**: 每个图表都有清晰的标签和说明

### 4. 📈 故障影响量化指标设计
- **综合影响因子(CIF)**: 多维度量化故障影响
- **数学公式**: CIF = w₁×THD + w₂×功率 + w₃×电压 + w₄×频率 + w₅×谐波
- **分级标准**: 轻微(≤0.3) | 中等(0.3-0.6) | 严重(>0.6)
- **权重配置**: THD(30%) + 功率(25%) + 电压(20%) + 频率(15%) + 谐波(10%)

## 🎯 核心功能

### 特征提取
- **时域特征**: RMS、峰值、偏度、峰度、波峰因子
- **频域特征**: 基波、谐波(3,5,7,11,13,15,17,19次)、THD
- **小波特征**: 多层分解、能量分布、小波熵
- **电能质量指标**: 电压偏差、功率因数、频率偏差

### 故障影响分析
- **定量评估**: 故障前后对比分析
- **影响排序**: 按影响程度排序特征
- **综合评价**: 多维度综合影响因子
- **严重程度**: 自动分级评估

### 可视化分析
- **时间序列**: 关键特征变化趋势
- **影响分布**: 各分量影响占比
- **对比分析**: 故障前后数据对比
- **公式展示**: 计算过程可视化

## 📋 使用指南

### 环境要求
```bash
# 基础依赖（必需）
pip install numpy pandas scipy matplotlib

# 可选依赖（增强功能）
pip install seaborn pywavelets
```

### 快速开始
1. **准备数据**: 确保 `pv_fault_data.mat` 文件在项目目录
2. **运行分析**: 打开 `pv_fault_analysis_improved.ipynb`
3. **执行所有单元格**: 点击 "Run All"
4. **查看结果**: 检查 `results/` 文件夹中的输出

### 输出文件说明
```
results/
├── plots/
│   ├── three_phase_currents_separated.png      # 三相电流分离图
│   ├── voltage_and_power_analysis.png          # 电压功率分析图
│   ├── improved_fault_impact_timeseries.png    # 故障影响时序图
│   └── comprehensive_impact_factor_analysis.png # 综合影响因子图
├── data/
│   ├── improved_features.csv                   # 完整特征数据
│   ├── improved_impact_analysis.csv            # 影响分析结果
│   └── comprehensive_impact_factor.csv         # 综合影响因子
└── reports/
    └── improved_analysis_report.md              # 详细分析报告
```

## 🔬 技术特点

### 改进的THD计算
```python
# 修正前：简单FFT
thd = sqrt(harmonics_power) / fundamental

# 修正后：精确计算
windowed_signal = signal * hanning_window
fundamental = precise_peak_detection(fft_result)
thd = sqrt(sum(harmonic_peaks²)) / fundamental
```

### 综合影响因子公式
```
CIF = 0.3×THD_impact + 0.25×Power_impact + 0.2×Voltage_impact + 
      0.15×Frequency_impact + 0.1×Harmonic_impact

其中：
- THD_impact = |THD变化率| + 超标恶化程度
- Power_impact = |功率损失率| + 相对额定损失
- Voltage_impact = |电压偏差变化| + 超标程度
- Frequency_impact = |频率偏差变化| + 超标程度
- Harmonic_impact = 各次谐波变化的平均值
```

### 严重程度分级
- **轻微影响** (CIF ≤ 0.3): 对电网影响较小，可接受
- **中等影响** (0.3 < CIF ≤ 0.6): 需要关注，建议采取措施
- **严重影响** (CIF > 0.6): 严重影响电网，必须立即处理

## 📊 分析结果示例

### 关键发现
- **THD变化**: 故障后THD从2.1%增加到3.8%，增幅81%
- **功率损失**: 约100kW功率损失，占总容量25%
- **综合影响因子**: 0.45（中等影响级别）
- **最显著特征**: ia_thd_percent、active_power、ia_rms

### MPC补偿建议
1. **THD补偿优先** - 权重30%，重点抑制3次、5次谐波
2. **功率调节** - 权重25%，调整剩余阵列输出补偿损失
3. **电压支撑** - 权重20%，优化直流侧电压控制
4. **预测控制参数** - 预测时域20步，控制时域5步

## 🔄 与原版本对比

| 改进项目 | 原版本 | 改进版本 |
|---------|--------|----------|
| 文件管理 | 散乱存储 | 统一文件夹结构 |
| THD计算 | 基础FFT | 汉宁窗+精确峰值检测 |
| 可视化 | 图表重合 | 分离显示+多样化线型 |
| 影响评估 | 定性描述 | 定量综合影响因子 |
| 报告质量 | 简单摘要 | 详细技术报告 |

## 🎯 应用价值

### 学术价值
- 提供标准化的光伏故障分析方法
- 建立量化的电能质量影响评估体系
- 为相关研究提供可复现的分析框架

### 工程价值
- 为MPC控制器设计提供精确输入特征
- 支持实时故障检测和预测系统开发
- 实现光伏故障的主动补偿和无感切换

### 创新点
- **量化评估**: 首次提出综合故障影响因子
- **精确计算**: 修正THD计算，确保物理意义
- **系统化**: 建立完整的分析和评估体系

## 🚀 后续发展

### 短期目标
1. 基于提取特征开发MPC控制器
2. 验证综合影响因子的有效性
3. 扩展到其他类型光伏故障

### 长期目标
1. 集成到实际光伏系统中
2. 开发商业化故障诊断产品
3. 建立行业标准和规范

## 📞 技术支持

如有问题请：
1. 检查数据文件和环境配置
2. 查看详细的分析报告
3. 参考改进说明文档
4. 运行环境检查脚本

---

**版本**: v2.0 改进版  
**更新时间**: 2024年  
**技术特色**: 四项核心改进，全面提升分析精度和实用性
