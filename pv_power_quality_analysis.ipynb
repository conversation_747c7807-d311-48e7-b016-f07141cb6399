# 导入必要的库
import numpy as np
import pandas as pd
import scipy.io
import scipy.stats
import pywt
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal
from scipy.fft import fft, fftfreq
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.metrics import mean_squared_error
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')
sns.set_palette("husl")

print("✅ 所有库导入成功！")
print(f"NumPy版本: {np.__version__}")
print(f"Pandas版本: {pd.__version__}")
print(f"SciPy版本: {scipy.__version__}")

# ===================================================================
# 电能质量分析专用配置类
# ===================================================================
class PowerQualityConfig:
    """电能质量分析和MPC补偿算法专用配置类"""
    
    # -- 基础仿真参数 --
    MAT_FILE_PATH = 'pv_fault_data.mat'  # .mat文件路径
    SIM_TIME = 3.0                       # 总仿真时间 (秒)
    FAULT_TIME = 1.0                     # 故障发生时间 (秒)
    GRID_FREQ = 50.0                     # 电网基波频率 (Hz)
    RATED_POWER = 400000                 # 系统额定功率 (W)
    RATED_VOLTAGE = 380                  # 额定线电压 (V)
    RATED_CURRENT = 608                  # 额定电流 (A)
    
    # -- 窗口化分析参数 --
    CYCLES_PER_WINDOW = 2                # 每个窗口包含的工频周期数
    WINDOW_OVERLAP = 0.5                 # 窗口重叠率 (50%)
    
    # -- 电能质量标准参数 (GB/T 14549-1993) --
    THD_LIMIT = 0.05                     # 总谐波畸变率限值 (5%)
    VOLTAGE_DEVIATION_LIMIT = 0.07       # 电压偏差限值 (±7%)
    FREQUENCY_DEVIATION_LIMIT = 0.2      # 频率偏差限值 (±0.2Hz)
    FLICKER_LIMIT = 1.0                  # 闪变限值
    
    # -- 谐波分析参数 --
    HARMONICS_TO_ANALYZE = [2, 3, 5, 7, 11, 13, 15, 17, 19, 21, 23, 25]  # 扩展谐波分析
    INTERHARMONICS = [1.5, 2.5, 3.5, 4.5, 6.5, 8.5]  # 间谐波分析
    SUBHARMONICS = [0.5, 1.5]            # 次谐波分析
    
    # -- 小波分析参数 --
    WAVELET_TYPE = 'db6'                 # 小波基函数 (db6对电力信号效果更好)
    WAVELET_LEVEL = 6                    # 分解层数
    
    # -- 故障分析参数 --
    PRE_FAULT_ANALYSIS_WINDOWS = 15      # 故障前分析窗口数
    POST_FAULT_STABLE_OFFSET = 10        # 故障后稳态偏移窗口数
    TRANSIENT_ANALYSIS_WINDOWS = 20      # 暂态过程分析窗口数
    
    # -- MPC补偿算法相关参数 --
    PREDICTION_HORIZON = 20              # 预测时域
    CONTROL_HORIZON = 5                  # 控制时域
    COMPENSATION_DELAY = 0.02            # 补偿延迟时间 (秒)
    SAMPLING_TIME = 0.001                # 控制采样时间 (秒)
    
    # -- 可视化参数 --
    FIGURE_SIZE = (15, 10)
    DPI = 300
    SAVE_PLOTS = True
    PLOT_FORMAT = 'png'
    
    # -- 数据导出参数 --
    EXPORT_FEATURES = True
    EXPORT_FORMAT = 'csv'
    FEATURE_FILE_NAME = 'pv_fault_features.csv'
    
print("✅ 电能质量分析配置加载完成！")

# ===================================================================
# 数据加载与预处理模块
# ===================================================================

def load_and_validate_data(config):
    """
    从.mat文件加载数据并进行验证和预处理
    
    Returns:
        tuple: (data_dict, sampling_rate, time_vector)
    """
    print("\n" + "="*60)
    print("📊 步骤1: 数据加载与验证")
    print("="*60)
    
    try:
        mat_data = scipy.io.loadmat(config.MAT_FILE_PATH)
        print(f"✅ 文件 '{config.MAT_FILE_PATH}' 加载成功!")
        print(f"📋 文件中包含的变量: {list(mat_data.keys())}")
    except FileNotFoundError:
        print(f"❌ 错误: 未找到文件 '{config.MAT_FILE_PATH}'")
        return None, None, None
    except Exception as e:
        print(f"❌ 文件加载错误: {e}")
        return None, None, None

    # 定义需要提取的变量
    required_vars = ['Ia', 'Ib', 'Ic', 'Va', 'Vb', 'Vc', 'Vdc']
    optional_vars = ['Grid_current', 'PQ', 'THD', 'I1', 'I2', 'I3', 'I4']
    
    # 提取数据并验证
    data_dict = {}
    for var in required_vars:
        if var in mat_data:
            data_dict[var] = mat_data[var].flatten()
            print(f"✅ {var}: {len(data_dict[var])} 个采样点")
        else:
            print(f"❌ 缺少关键变量: {var}")
            return None, None, None
    
    # 提取可选变量
    for var in optional_vars:
        if var in mat_data:
            data_dict[var] = mat_data[var].flatten()
            print(f"📈 {var}: {len(data_dict[var])} 个采样点 (可选)")
    
    # 计算采样参数
    num_samples = len(data_dict['Ia'])
    sampling_rate = num_samples / config.SIM_TIME
    time_vector = np.linspace(0, config.SIM_TIME, num_samples)
    
    print(f"\n📊 采样信息:")
    print(f"   - 仿真时长: {config.SIM_TIME} 秒")
    print(f"   - 采样点数: {num_samples}")
    print(f"   - 采样频率: {sampling_rate:.2f} Hz")
    print(f"   - 采样间隔: {1/sampling_rate*1000:.3f} ms")
    
    # 数据质量检查
    print(f"\n🔍 数据质量检查:")
    for var_name, var_data in data_dict.items():
        if var_name in required_vars:
            nan_count = np.isnan(var_data).sum()
            inf_count = np.isinf(var_data).sum()
            if nan_count > 0 or inf_count > 0:
                print(f"⚠️  {var_name}: {nan_count} NaN值, {inf_count} 无穷值")
            else:
                print(f"✅ {var_name}: 数据完整")
    
    return data_dict, sampling_rate, time_vector

def plot_raw_data_overview(data_dict, time_vector, config):
    """
    绘制原始数据概览图
    """
    fig, axes = plt.subplots(3, 2, figsize=config.FIGURE_SIZE)
    fig.suptitle('光伏系统原始数据概览', fontsize=16, fontweight='bold')
    
    # 故障时间线
    fault_time = config.FAULT_TIME
    
    # 三相电流
    axes[0, 0].plot(time_vector, data_dict['Ia'], label='Ia', alpha=0.8)
    axes[0, 0].plot(time_vector, data_dict['Ib'], label='Ib', alpha=0.8)
    axes[0, 0].plot(time_vector, data_dict['Ic'], label='Ic', alpha=0.8)
    axes[0, 0].axvline(fault_time, color='red', linestyle='--', label='故障时刻')
    axes[0, 0].set_title('三相电流')
    axes[0, 0].set_ylabel('电流 (A)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 三相电压
    axes[0, 1].plot(time_vector, data_dict['Va'], label='Va', alpha=0.8)
    axes[0, 1].plot(time_vector, data_dict['Vb'], label='Vb', alpha=0.8)
    axes[0, 1].plot(time_vector, data_dict['Vc'], label='Vc', alpha=0.8)
    axes[0, 1].axvline(fault_time, color='red', linestyle='--', label='故障时刻')
    axes[0, 1].set_title('三相电压')
    axes[0, 1].set_ylabel('电压 (V)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 直流母线电压
    axes[1, 0].plot(time_vector, data_dict['Vdc'], color='purple', linewidth=2)
    axes[1, 0].axvline(fault_time, color='red', linestyle='--', label='故障时刻')
    axes[1, 0].set_title('直流母线电压')
    axes[1, 0].set_ylabel('电压 (V)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # A相电流放大图（故障前后）
    start_idx = int((fault_time - 0.1) * len(time_vector) / config.SIM_TIME)
    end_idx = int((fault_time + 0.1) * len(time_vector) / config.SIM_TIME)
    axes[1, 1].plot(time_vector[start_idx:end_idx], data_dict['Ia'][start_idx:end_idx], 
                   color='blue', linewidth=2)
    axes[1, 1].axvline(fault_time, color='red', linestyle='--', label='故障时刻')
    axes[1, 1].set_title('A相电流 (故障前后0.1秒)')
    axes[1, 1].set_ylabel('电流 (A)')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 功率计算和显示
    p_inst = (data_dict['Va'] * data_dict['Ia'] + 
              data_dict['Vb'] * data_dict['Ib'] + 
              data_dict['Vc'] * data_dict['Ic'])
    axes[2, 0].plot(time_vector, p_inst/1000, color='green', linewidth=2)
    axes[2, 0].axvline(fault_time, color='red', linestyle='--', label='故障时刻')
    axes[2, 0].set_title('瞬时功率')
    axes[2, 0].set_ylabel('功率 (kW)')
    axes[2, 0].set_xlabel('时间 (s)')
    axes[2, 0].legend()
    axes[2, 0].grid(True, alpha=0.3)
    
    # 电流有效值趋势
    window_size = int(0.02 * len(time_vector) / config.SIM_TIME)  # 20ms窗口
    ia_rms = np.array([np.sqrt(np.mean(data_dict['Ia'][i:i+window_size]**2)) 
                      for i in range(0, len(data_dict['Ia'])-window_size, window_size//2)])
    time_rms = time_vector[::window_size//2][:len(ia_rms)]
    axes[2, 1].plot(time_rms, ia_rms, color='orange', linewidth=2)
    axes[2, 1].axvline(fault_time, color='red', linestyle='--', label='故障时刻')
    axes[2, 1].set_title('A相电流有效值趋势')
    axes[2, 1].set_ylabel('电流有效值 (A)')
    axes[2, 1].set_xlabel('时间 (s)')
    axes[2, 1].legend()
    axes[2, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    if config.SAVE_PLOTS:
        plt.savefig(f'raw_data_overview.{config.PLOT_FORMAT}', dpi=config.DPI, bbox_inches='tight')
    plt.show()
    
    print("✅ 原始数据概览图绘制完成")

# ===================================================================
# 电能质量特征提取模块
# ===================================================================

class PowerQualityFeatureExtractor:
    """电能质量特征提取器"""
    
    def __init__(self, config):
        self.config = config
        
    def extract_time_domain_features(self, signal_data):
        """提取时域特征"""
        features = {}
        
        # 基础统计特征
        features['rms'] = np.sqrt(np.mean(signal_data**2))
        features['peak'] = np.max(np.abs(signal_data))
        features['mean'] = np.mean(signal_data)
        features['std'] = np.std(signal_data)
        features['variance'] = np.var(signal_data)
        features['skewness'] = scipy.stats.skew(signal_data)
        features['kurtosis'] = scipy.stats.kurtosis(signal_data)
        
        # 波形因子
        if features['rms'] != 0:
            features['crest_factor'] = features['peak'] / features['rms']
            features['form_factor'] = features['rms'] / np.mean(np.abs(signal_data))
        else:
            features['crest_factor'] = 0
            features['form_factor'] = 0
            
        # 能量特征
        features['energy'] = np.sum(signal_data**2)
        features['power'] = features['energy'] / len(signal_data)
        
        return features
    
    def extract_frequency_domain_features(self, signal_data, fs):
        """提取频域特征"""
        features = {}
        N = len(signal_data)
        
        # FFT分析
        yf = fft(signal_data)
        yf_mag = 2.0/N * np.abs(yf[:N//2])
        freqs = fftfreq(N, 1/fs)[:N//2]
        
        # 基波分析
        fundamental_idx = np.argmin(np.abs(freqs - self.config.GRID_FREQ))
        features['fundamental_magnitude'] = yf_mag[fundamental_idx]
        features['fundamental_phase'] = np.angle(yf[fundamental_idx])
        
        # 谐波分析
        harmonics_power = 0
        for h in self.config.HARMONICS_TO_ANALYZE:
            harmonic_freq = h * self.config.GRID_FREQ
            harmonic_idx = np.argmin(np.abs(freqs - harmonic_freq))
            if harmonic_idx < len(yf_mag):
                harmonic_mag = yf_mag[harmonic_idx]
                features[f'harmonic_{h}_magnitude'] = harmonic_mag
                features[f'harmonic_{h}_phase'] = np.angle(yf[harmonic_idx])
                harmonics_power += harmonic_mag**2
        
        # THD计算
        if features['fundamental_magnitude'] != 0:
            features['thd'] = np.sqrt(harmonics_power) / features['fundamental_magnitude']
        else:
            features['thd'] = 0
            
        # 频谱重心
        features['spectral_centroid'] = np.sum(freqs * yf_mag) / np.sum(yf_mag)
        
        # 频谱扩散度
        features['spectral_spread'] = np.sqrt(np.sum(((freqs - features['spectral_centroid'])**2) * yf_mag) / np.sum(yf_mag))
        
        return features
    
    def extract_wavelet_features(self, signal_data):
        """提取小波域特征"""
        features = {}
        
        # 小波分解
        coeffs = pywt.wavedec(signal_data, self.config.WAVELET_TYPE, level=self.config.WAVELET_LEVEL)
        
        # 各层能量
        for i in range(self.config.WAVELET_LEVEL):
            features[f'wavelet_energy_d{i+1}'] = np.sum(coeffs[i+1]**2)
            features[f'wavelet_energy_ratio_d{i+1}'] = features[f'wavelet_energy_d{i+1}'] / np.sum([np.sum(c**2) for c in coeffs])
        
        # 近似系数能量
        features['wavelet_energy_a'] = np.sum(coeffs[0]**2)
        
        # 小波熵
        total_energy = sum([np.sum(c**2) for c in coeffs])
        if total_energy > 0:
            energies = [np.sum(c**2)/total_energy for c in coeffs]
            features['wavelet_entropy'] = -np.sum([e*np.log2(e) for e in energies if e > 0])
        else:
            features['wavelet_entropy'] = 0
            
        return features
    
    def extract_power_quality_indicators(self, voltage_data, current_data, fs):
        """提取电能质量指标"""
        features = {}
        
        # 电压质量指标
        v_rms = np.sqrt(np.mean(voltage_data**2))
        features['voltage_deviation'] = (v_rms - self.config.RATED_VOLTAGE) / self.config.RATED_VOLTAGE
        features['voltage_unbalance'] = np.std([np.sqrt(np.mean(v**2)) for v in [voltage_data]]) / v_rms
        
        # 电流质量指标
        i_rms = np.sqrt(np.mean(current_data**2))
        features['current_deviation'] = (i_rms - self.config.RATED_CURRENT) / self.config.RATED_CURRENT
        
        # 功率因数
        p_inst = voltage_data * current_data
        features['active_power'] = np.mean(p_inst)
        features['apparent_power'] = v_rms * i_rms
        if features['apparent_power'] != 0:
            features['power_factor'] = features['active_power'] / features['apparent_power']
        else:
            features['power_factor'] = 0
            
        # 频率偏差（通过零交叉点检测）
        zero_crossings = np.where(np.diff(np.signbit(voltage_data)))[0]
        if len(zero_crossings) > 2:
            periods = np.diff(zero_crossings[::2]) / fs  # 每个周期的时间
            avg_period = np.mean(periods)
            measured_freq = 1 / avg_period if avg_period > 0 else 0
            features['frequency_deviation'] = measured_freq - self.config.GRID_FREQ
        else:
            features['frequency_deviation'] = 0
            
        return features
    
    def extract_all_features_for_window(self, window_data, fs):
        """为单个窗口提取所有特征"""
        all_features = {}
        
        # 对每相电流和电压提取特征
        phases = ['a', 'b', 'c']
        for i, phase in enumerate(phases):
            current_key = ['Ia', 'Ib', 'Ic'][i]
            voltage_key = ['Va', 'Vb', 'Vc'][i]
            
            if current_key in window_data and voltage_key in window_data:
                # 电流特征
                current_features = self.extract_time_domain_features(window_data[current_key])
                for key, value in current_features.items():
                    all_features[f'i{phase}_{key}'] = value
                
                freq_features = self.extract_frequency_domain_features(window_data[current_key], fs)
                for key, value in freq_features.items():
                    all_features[f'i{phase}_{key}'] = value
                
                wavelet_features = self.extract_wavelet_features(window_data[current_key])
                for key, value in wavelet_features.items():
                    all_features[f'i{phase}_{key}'] = value
                
                # 电压特征
                voltage_features = self.extract_time_domain_features(window_data[voltage_key])
                for key, value in voltage_features.items():
                    all_features[f'v{phase}_{key}'] = value
                
                # 电能质量指标
                pq_features = self.extract_power_quality_indicators(
                    window_data[voltage_key], window_data[current_key], fs)
                for key, value in pq_features.items():
                    all_features[f'{phase}_{key}'] = value
        
        # 直流侧特征
        if 'Vdc' in window_data:
            dc_features = self.extract_time_domain_features(window_data['Vdc'])
            for key, value in dc_features.items():
                all_features[f'vdc_{key}'] = value
        
        # 三相总体特征
        if all(['Ia', 'Ib', 'Ic'] in window_data.keys()):
            # 三相不平衡度
            i_rms = [np.sqrt(np.mean(window_data[f'I{phase}']**2)) for phase in ['a', 'b', 'c']]
            all_features['current_unbalance'] = np.std(i_rms) / np.mean(i_rms) if np.mean(i_rms) > 0 else 0
            
            # 零序电流
            i_zero = (window_data['Ia'] + window_data['Ib'] + window_data['Ic']) / 3
            all_features['zero_sequence_current'] = np.sqrt(np.mean(i_zero**2))
        
        return all_features

# ===================================================================
# 数据分窗与特征提取主流程
# ===================================================================

def perform_windowed_analysis(data_dict, sampling_rate, config):
    """
    执行分窗分析和特征提取
    
    Returns:
        tuple: (feature_df, window_info)
    """
    print("\n" + "="*60)
    print("🔍 步骤2: 分窗分析与特征提取")
    print("="*60)
    
    # 计算窗口参数
    window_size = int(sampling_rate * config.CYCLES_PER_WINDOW / config.GRID_FREQ)
    step_size = int(window_size * (1 - config.WINDOW_OVERLAP))
    num_windows = (len(data_dict['Ia']) - window_size) // step_size + 1
    
    print(f"📊 分窗参数:")
    print(f"   - 每窗口周期数: {config.CYCLES_PER_WINDOW}")
    print(f"   - 窗口大小: {window_size} 点 ({window_size/sampling_rate*1000:.1f} ms)")
    print(f"   - 步长: {step_size} 点 ({step_size/sampling_rate*1000:.1f} ms)")
    print(f"   - 重叠率: {config.WINDOW_OVERLAP*100:.1f}%")
    print(f"   - 总窗口数: {num_windows}")
    
    # 初始化特征提取器
    feature_extractor = PowerQualityFeatureExtractor(config)
    
    # 存储所有窗口的特征
    all_features = []
    window_info = []
    
    print(f"\n🔄 开始特征提取...")
    for i in range(num_windows):
        start_idx = i * step_size
        end_idx = start_idx + window_size
        window_center_time = (start_idx + end_idx) / 2 / sampling_rate
        
        # 创建当前窗口数据
        current_window_data = {}
        for key, data in data_dict.items():
            current_window_data[key] = data[start_idx:end_idx]
        
        # 提取特征
        features = feature_extractor.extract_all_features_for_window(
            current_window_data, sampling_rate)
        
        # 添加窗口信息
        features['window_index'] = i
        features['window_center_time'] = window_center_time
        features['window_start_time'] = start_idx / sampling_rate
        features['window_end_time'] = end_idx / sampling_rate
        
        all_features.append(features)
        window_info.append({
            'index': i,
            'start_idx': start_idx,
            'end_idx': end_idx,
            'center_time': window_center_time
        })
        
        if (i + 1) % 20 == 0:
            print(f"   已处理 {i+1}/{num_windows} 个窗口")
    
    # 转换为DataFrame
    feature_df = pd.DataFrame(all_features)
    
    print(f"✅ 特征提取完成!")
    print(f"   - 提取特征数: {len(feature_df.columns)}")
    print(f"   - 数据维度: {feature_df.shape}")
    
    return feature_df, window_info

def add_fault_labels(feature_df, config):
    """
    为特征数据添加故障标签
    """
    print(f"\n🏷️  添加故障标签...")
    
    # 创建标签
    feature_df['fault_label'] = 0  # 0: 正常, 1: 故障
    feature_df['fault_phase'] = 'normal'  # 故障阶段标识
    
    # 根据时间标记故障
    fault_mask = feature_df['window_center_time'] >= config.FAULT_TIME
    feature_df.loc[fault_mask, 'fault_label'] = 1
    
    # 细分故障阶段
    pre_fault_mask = feature_df['window_center_time'] < config.FAULT_TIME
    transient_mask = ((feature_df['window_center_time'] >= config.FAULT_TIME) & 
                     (feature_df['window_center_time'] < config.FAULT_TIME + 0.2))  # 故障后200ms为暂态
    post_fault_mask = feature_df['window_center_time'] >= config.FAULT_TIME + 0.2
    
    feature_df.loc[pre_fault_mask, 'fault_phase'] = 'pre_fault'
    feature_df.loc[transient_mask, 'fault_phase'] = 'transient'
    feature_df.loc[post_fault_mask, 'fault_phase'] = 'post_fault'
    
    # 统计信息
    phase_counts = feature_df['fault_phase'].value_counts()
    print(f"   故障阶段分布:")
    for phase, count in phase_counts.items():
        print(f"     - {phase}: {count} 个窗口")
    
    return feature_df

# ===================================================================
# 故障影响分析与可视化
# ===================================================================

def calculate_fault_impact_vector(feature_df, config):
    """
    计算故障影响向量
    """
    print("\n" + "="*60)
    print("📈 步骤3: 故障影响向量计算")
    print("="*60)
    
    # 选择故障前后的稳态窗口
    pre_fault_windows = feature_df[feature_df['fault_phase'] == 'pre_fault'].tail(config.PRE_FAULT_ANALYSIS_WINDOWS)
    post_fault_windows = feature_df[feature_df['fault_phase'] == 'post_fault'].head(config.POST_FAULT_STABLE_OFFSET)
    
    if len(pre_fault_windows) == 0 or len(post_fault_windows) == 0:
        print("❌ 错误: 无法找到足够的故障前后稳态窗口")
        return None
    
    print(f"📊 分析窗口:")
    print(f"   - 故障前稳态窗口: {len(pre_fault_windows)} 个")
    print(f"   - 故障后稳态窗口: {len(post_fault_windows)} 个")
    
    # 排除非数值列
    numeric_columns = feature_df.select_dtypes(include=[np.number]).columns
    exclude_columns = ['window_index', 'window_center_time', 'window_start_time', 'window_end_time', 'fault_label']
    feature_columns = [col for col in numeric_columns if col not in exclude_columns]
    
    # 计算故障前后的平均值
    pre_fault_mean = pre_fault_windows[feature_columns].mean()
    post_fault_mean = post_fault_windows[feature_columns].mean()
    
    # 计算影响向量（绝对变化和相对变化）
    absolute_impact = post_fault_mean - pre_fault_mean
    relative_impact = (post_fault_mean - pre_fault_mean) / (pre_fault_mean + 1e-10) * 100  # 避免除零
    
    # 创建影响分析DataFrame
    impact_analysis = pd.DataFrame({
        'Pre_Fault_Mean': pre_fault_mean,
        'Post_Fault_Mean': post_fault_mean,
        'Absolute_Impact': absolute_impact,
        'Relative_Impact_Percent': relative_impact,
        'Impact_Magnitude': np.abs(relative_impact)
    })
    
    # 按影响程度排序
    impact_analysis = impact_analysis.sort_values('Impact_Magnitude', ascending=False)
    
    print(f"\n🎯 故障影响最显著的前10个特征:")
    print(impact_analysis.head(10)[['Relative_Impact_Percent', 'Absolute_Impact']].round(4))
    
    return impact_analysis

def plot_fault_impact_analysis(feature_df, impact_analysis, config):
    """
    绘制故障影响分析图
    """
    fig, axes = plt.subplots(2, 2, figsize=config.FIGURE_SIZE)
    fig.suptitle('光伏断路故障影响分析', fontsize=16, fontweight='bold')
    
    # 1. 关键特征时间序列
    key_features = ['ia_rms', 'ia_thd', 'a_active_power', 'vdc_rms']
    colors = ['blue', 'red', 'green', 'purple']
    
    for i, (feature, color) in enumerate(zip(key_features, colors)):
        if feature in feature_df.columns:
            axes[0, 0].plot(feature_df['window_center_time'], feature_df[feature], 
                           label=feature, color=color, alpha=0.8)
    
    axes[0, 0].axvline(config.FAULT_TIME, color='red', linestyle='--', linewidth=2, label='故障时刻')
    axes[0, 0].set_title('关键电能质量特征时间序列')
    axes[0, 0].set_xlabel('时间 (s)')
    axes[0, 0].set_ylabel('特征值')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 故障影响程度排序
    top_impacts = impact_analysis.head(15)
    y_pos = np.arange(len(top_impacts))
    
    bars = axes[0, 1].barh(y_pos, top_impacts['Relative_Impact_Percent'], 
                          color=['red' if x > 0 else 'blue' for x in top_impacts['Relative_Impact_Percent']])
    axes[0, 1].set_yticks(y_pos)
    axes[0, 1].set_yticklabels(top_impacts.index, fontsize=8)
    axes[0, 1].set_title('故障影响程度排序 (相对变化%)')
    axes[0, 1].set_xlabel('相对变化 (%)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. THD变化分析
    if 'ia_thd' in feature_df.columns:
        pre_fault_data = feature_df[feature_df['fault_phase'] == 'pre_fault']['ia_thd']
        post_fault_data = feature_df[feature_df['fault_phase'] == 'post_fault']['ia_thd']
        
        axes[1, 0].hist(pre_fault_data, bins=20, alpha=0.7, label='故障前', color='blue')
        axes[1, 0].hist(post_fault_data, bins=20, alpha=0.7, label='故障后', color='red')
        axes[1, 0].axvline(config.THD_LIMIT, color='orange', linestyle='--', linewidth=2, label='国标限值(5%)')
        axes[1, 0].set_title('THD分布对比')
        axes[1, 0].set_xlabel('THD')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 功率变化分析
    if 'a_active_power' in feature_df.columns:
        power_data = feature_df['a_active_power'] / 1000  # 转换为kW
        axes[1, 1].plot(feature_df['window_center_time'], power_data, 
                       color='green', linewidth=2, label='有功功率')
        axes[1, 1].axvline(config.FAULT_TIME, color='red', linestyle='--', linewidth=2, label='故障时刻')
        
        # 添加功率损失标注
        pre_power = feature_df[feature_df['fault_phase'] == 'pre_fault']['a_active_power'].mean() / 1000
        post_power = feature_df[feature_df['fault_phase'] == 'post_fault']['a_active_power'].mean() / 1000
        power_loss = pre_power - post_power
        
        axes[1, 1].text(0.02, 0.98, f'功率损失: {power_loss:.1f} kW\n损失率: {power_loss/pre_power*100:.1f}%', 
                        transform=axes[1, 1].transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        axes[1, 1].set_title('有功功率变化')
        axes[1, 1].set_xlabel('时间 (s)')
        axes[1, 1].set_ylabel('功率 (kW)')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    if config.SAVE_PLOTS:
        plt.savefig(f'fault_impact_analysis.{config.PLOT_FORMAT}', dpi=config.DPI, bbox_inches='tight')
    plt.show()
    
    print("✅ 故障影响分析图绘制完成")

# ===================================================================
# MPC补偿算法特征准备模块
# ===================================================================

def prepare_mpc_features(feature_df, impact_analysis, config):
    """
    为MPC补偿算法准备特征数据
    """
    print("\n" + "="*60)
    print("🎛️  步骤4: MPC补偿算法特征准备")
    print("="*60)
    
    # 选择对故障最敏感的特征作为MPC输入
    top_sensitive_features = impact_analysis.head(20).index.tolist()
    
    # 添加关键电能质量指标
    essential_features = ['ia_rms', 'ia_thd', 'ia_fundamental_magnitude', 
                         'a_active_power', 'a_power_factor', 'vdc_rms']
    
    mpc_features = list(set(top_sensitive_features + essential_features))
    mpc_features = [f for f in mpc_features if f in feature_df.columns]
    
    print(f"📊 MPC特征选择:")
    print(f"   - 选择特征数: {len(mpc_features)}")
    print(f"   - 关键特征: {essential_features}")
    
    # 创建MPC训练数据集
    mpc_data = feature_df[mpc_features + ['window_center_time', 'fault_label', 'fault_phase']].copy()
    
    # 数据标准化
    scaler = StandardScaler()
    mpc_data_scaled = mpc_data.copy()
    mpc_data_scaled[mpc_features] = scaler.fit_transform(mpc_data[mpc_features])
    
    # 计算补偿目标值（故障前的稳态值）
    pre_fault_targets = feature_df[feature_df['fault_phase'] == 'pre_fault'][mpc_features].mean()
    
    # 计算需要补偿的量
    compensation_requirements = {}
    for feature in mpc_features:
        if feature in impact_analysis.index:
            compensation_requirements[feature] = {
                'target_value': pre_fault_targets[feature],
                'impact_magnitude': impact_analysis.loc[feature, 'Absolute_Impact'],
                'relative_impact': impact_analysis.loc[feature, 'Relative_Impact_Percent'],
                'compensation_priority': abs(impact_analysis.loc[feature, 'Relative_Impact_Percent'])
            }
    
    # 按补偿优先级排序
    compensation_df = pd.DataFrame(compensation_requirements).T
    compensation_df = compensation_df.sort_values('compensation_priority', ascending=False)
    
    print(f"\n🎯 补偿优先级排序 (前10项):")
    print(compensation_df.head(10)[['relative_impact', 'impact_magnitude']].round(4))
    
    # 生成MPC控制序列建议
    control_suggestions = generate_control_suggestions(compensation_df, config)
    
    return {
        'mpc_features': mpc_features,
        'mpc_data': mpc_data,
        'mpc_data_scaled': mpc_data_scaled,
        'scaler': scaler,
        'compensation_requirements': compensation_df,
        'control_suggestions': control_suggestions
    }

def generate_control_suggestions(compensation_df, config):
    """
    生成MPC控制建议
    """
    suggestions = {
        'reactive_power_compensation': 0,
        'harmonic_filtering': {},
        'voltage_regulation': 0,
        'power_factor_correction': 0
    }
    
    # 基于特征影响程度生成控制建议
    for feature, data in compensation_df.iterrows():
        if 'thd' in feature:
            # 谐波补偿建议
            harmonic_num = feature.split('_')[1] if '_' in feature else 'total'
            suggestions['harmonic_filtering'][harmonic_num] = {
                'compensation_level': min(abs(data['relative_impact']) / 100, 1.0),
                'priority': data['compensation_priority']
            }
        elif 'power_factor' in feature:
            # 功率因数校正建议
            suggestions['power_factor_correction'] = abs(data['impact_magnitude'])
        elif 'voltage' in feature:
            # 电压调节建议
            suggestions['voltage_regulation'] = abs(data['impact_magnitude'])
        elif 'reactive_power' in feature:
            # 无功补偿建议
            suggestions['reactive_power_compensation'] = abs(data['impact_magnitude'])
    
    return suggestions

def plot_mpc_preparation_analysis(mpc_results, config):
    """
    绘制MPC准备分析图
    """
    fig, axes = plt.subplots(2, 2, figsize=config.FIGURE_SIZE)
    fig.suptitle('MPC补偿算法特征准备分析', fontsize=16, fontweight='bold')
    
    # 1. 特征重要性
    top_features = mpc_results['compensation_requirements'].head(10)
    y_pos = np.arange(len(top_features))
    
    axes[0, 0].barh(y_pos, top_features['compensation_priority'], color='skyblue')
    axes[0, 0].set_yticks(y_pos)
    axes[0, 0].set_yticklabels(top_features.index, fontsize=8)
    axes[0, 0].set_title('MPC特征重要性排序')
    axes[0, 0].set_xlabel('补偿优先级')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 补偿需求分析
    compensation_types = ['谐波补偿', '无功补偿', '电压调节', '功率因数校正']
    compensation_values = [
        len(mpc_results['control_suggestions']['harmonic_filtering']),
        mpc_results['control_suggestions']['reactive_power_compensation'],
        mpc_results['control_suggestions']['voltage_regulation'],
        mpc_results['control_suggestions']['power_factor_correction']
    ]
    
    axes[0, 1].pie(compensation_values, labels=compensation_types, autopct='%1.1f%%', startangle=90)
    axes[0, 1].set_title('补偿需求分布')
    
    # 3. 特征相关性热图
    key_features = mpc_results['mpc_features'][:10]  # 取前10个特征
    correlation_matrix = mpc_results['mpc_data'][key_features].corr()
    
    im = axes[1, 0].imshow(correlation_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)
    axes[1, 0].set_xticks(range(len(key_features)))
    axes[1, 0].set_yticks(range(len(key_features)))
    axes[1, 0].set_xticklabels(key_features, rotation=45, ha='right', fontsize=8)
    axes[1, 0].set_yticklabels(key_features, fontsize=8)
    axes[1, 0].set_title('特征相关性矩阵')
    plt.colorbar(im, ax=axes[1, 0])
    
    # 4. 控制时域分析
    time_horizon = np.arange(config.PREDICTION_HORIZON)
    
    # 模拟预测轨迹
    prediction_trajectory = np.exp(-time_horizon * 0.1) * 0.5  # 指数衰减
    control_trajectory = np.ones(config.CONTROL_HORIZON) * 0.3
    
    axes[1, 1].plot(time_horizon, prediction_trajectory, 'b-', linewidth=2, label='预测轨迹')
    axes[1, 1].plot(time_horizon[:config.CONTROL_HORIZON], control_trajectory, 
                   'r-', linewidth=3, label='控制时域')
    axes[1, 1].axhline(0, color='black', linestyle='--', alpha=0.5)
    axes[1, 1].set_title('MPC时域设计')
    axes[1, 1].set_xlabel('时间步')
    axes[1, 1].set_ylabel('控制量')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    if config.SAVE_PLOTS:
        plt.savefig(f'mpc_preparation_analysis.{config.PLOT_FORMAT}', dpi=config.DPI, bbox_inches='tight')
    plt.show()
    
    print("✅ MPC准备分析图绘制完成")

# ===================================================================
# 主执行流程
# ===================================================================

def main_analysis():
    """
    执行完整的光伏故障电能质量影响分析
    """
    print("🚀 开始光伏断路故障电能质量影响分析")
    print("="*80)
    
    # 初始化配置
    config = PowerQualityConfig()
    
    try:
        # 步骤1: 数据加载与验证
        data_dict, sampling_rate, time_vector = load_and_validate_data(config)
        if data_dict is None:
            return None
        
        # 绘制原始数据概览
        plot_raw_data_overview(data_dict, time_vector, config)
        
        # 步骤2: 分窗分析与特征提取
        feature_df, window_info = perform_windowed_analysis(data_dict, sampling_rate, config)
        
        # 添加故障标签
        feature_df = add_fault_labels(feature_df, config)
        
        # 步骤3: 故障影响分析
        impact_analysis = calculate_fault_impact_vector(feature_df, config)
        if impact_analysis is not None:
            plot_fault_impact_analysis(feature_df, impact_analysis, config)
        
        # 步骤4: MPC补偿特征准备
        mpc_results = prepare_mpc_features(feature_df, impact_analysis, config)
        plot_mpc_preparation_analysis(mpc_results, config)
        
        # 步骤5: 结果导出
        if config.EXPORT_FEATURES:
            export_results(feature_df, impact_analysis, mpc_results, config)
        
        # 生成分析报告
        generate_analysis_report(feature_df, impact_analysis, mpc_results, config)
        
        print("\n" + "="*80)
        print("🎉 分析完成！所有结果已保存。")
        print("="*80)
        
        return {
            'feature_df': feature_df,
            'impact_analysis': impact_analysis,
            'mpc_results': mpc_results,
            'config': config
        }
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def export_results(feature_df, impact_analysis, mpc_results, config):
    """
    导出分析结果
    """
    print(f"\n💾 导出分析结果...")
    
    # 导出特征数据
    feature_df.to_csv('pv_fault_features.csv', index=False)
    print(f"   ✅ 特征数据已导出: pv_fault_features.csv")
    
    # 导出影响分析
    impact_analysis.to_csv('fault_impact_analysis.csv')
    print(f"   ✅ 影响分析已导出: fault_impact_analysis.csv")
    
    # 导出MPC特征
    mpc_results['mpc_data'].to_csv('mpc_features.csv', index=False)
    mpc_results['compensation_requirements'].to_csv('compensation_requirements.csv')
    print(f"   ✅ MPC数据已导出: mpc_features.csv, compensation_requirements.csv")

def generate_analysis_report(feature_df, impact_analysis, mpc_results, config):
    """
    生成分析报告
    """
    print(f"\n📋 生成分析报告...")
    
    report = f"""
# 光伏断路故障电能质量影响分析报告

## 系统配置
- 系统容量: {config.RATED_POWER/1000} kW
- 故障时间: {config.FAULT_TIME} 秒
- 分析时长: {config.SIM_TIME} 秒
- 分析窗口数: {len(feature_df)}

## 关键发现
### 1. 电能质量影响
- 最大THD变化: {impact_analysis.loc[impact_analysis.index.str.contains('thd', case=False), 'Relative_Impact_Percent'].abs().max():.2f}%
- 功率损失: {abs(impact_analysis.loc[impact_analysis.index.str.contains('power', case=False), 'Absolute_Impact'].sum()/1000):.1f} kW
- 电压偏差: {impact_analysis.loc[impact_analysis.index.str.contains('voltage', case=False), 'Relative_Impact_Percent'].abs().max():.2f}%

### 2. MPC补偿建议
- 优先补偿特征数: {len(mpc_results['compensation_requirements'])}
- 谐波补偿需求: {len(mpc_results['control_suggestions']['harmonic_filtering'])} 个频次
- 无功补偿需求: {mpc_results['control_suggestions']['reactive_power_compensation']:.2f}

### 3. 控制策略建议
1. 实施主动谐波补偿，重点关注3次、5次谐波
2. 调整无功功率输出以维持电压稳定
3. 优化功率因数以减少电网冲击
4. 建议MPC预测时域: {config.PREDICTION_HORIZON} 步
5. 建议控制时域: {config.CONTROL_HORIZON} 步

## 结论
PV Array4断路故障对电网电能质量产生显著影响，通过本分析提取的特征
可为MPC补偿算法提供有效的输入，实现故障的主动补偿和电网支撑。
"""
    
    with open('analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"   ✅ 分析报告已生成: analysis_report.md")

# 执行分析
if __name__ == "__main__":
    results = main_analysis()

# 快速测试和验证
import os

def quick_validation():
    """快速验证环境和数据文件"""
    print("🔍 环境验证...")
    
    # 检查数据文件
    if os.path.exists('pv_fault_data.mat'):
        print("✅ 数据文件 pv_fault_data.mat 存在")
    else:
        print("❌ 数据文件 pv_fault_data.mat 不存在，请确保文件在当前目录下")
        return False
    
    # 检查必要的库
    try:
        import scipy.stats
        print("✅ SciPy.stats 可用")
    except ImportError:
        print("❌ SciPy.stats 不可用，某些统计特征可能无法计算")
    
    print("\n🚀 环境验证完成，可以开始分析！")
    print("💡 提示：运行 main_analysis() 开始完整分析")
    return True

# 运行验证
quick_validation()