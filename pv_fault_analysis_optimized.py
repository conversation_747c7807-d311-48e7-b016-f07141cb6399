"""
光伏断路故障电能质量影响分析系统 - 优化版

该模块提供了完整的光伏系统故障分析功能，包括：
- 数据加载和预处理
- 特征提取和分析
- 故障影响量化
- 可视化和报告生成

作者: 光伏系统分析团队
版本: 2.0
日期: 2025-07-18
"""

import os
import warnings
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union, Any

import numpy as np
import pandas as pd
import scipy.io
import scipy.stats
import matplotlib.pyplot as plt

# 可选库导入
try:
    import pywt
    PYWT_AVAILABLE = True
except ImportError:
    PYWT_AVAILABLE = False

try:
    import seaborn as sns
    sns.set_style("whitegrid")
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False

# 配置设置
warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class AnalysisConfig:
    """光伏故障分析配置参数类"""
    
    def __init__(self, mat_file_path: str = 'pv_fault_circult_data.mat'):
        """
        初始化配置参数
        
        Args:
            mat_file_path: MATLAB数据文件路径
        """
        # 基础参数
        self.MAT_FILE_PATH = mat_file_path
        self.SIM_TIME = 3.0                    # 仿真时间(秒)
        self.FAULT_TIME = 1.0                  # 故障时间(秒)
        self.GRID_FREQ = 60.0                  # 电网频率(Hz)
        self.RATED_POWER = 400000              # 额定功率(W)
        self.RATED_VOLTAGE = 380               # 额定电压(V)
        self.RATED_CURRENT = 608               # 额定电流(A)
        
        # 分窗参数
        self.CYCLES_PER_WINDOW = 2             # 每窗口周期数
        self.WINDOW_OVERLAP = 0.5              # 窗口重叠率
        
        # 电能质量标准
        self.THD_LIMIT = 0.05                  # THD限值(5%)
        self.VOLTAGE_DEVIATION_LIMIT = 0.07    # 电压偏差限值(±7%)
        self.FREQUENCY_DEVIATION_LIMIT = 0.24  # 频率偏差限值(±0.24Hz)
        
        # 谐波分析
        self.HARMONICS = [3, 5, 7, 11, 13, 15, 17, 19]
        
        # 小波分析
        self.WAVELET_TYPE = 'db4'
        self.WAVELET_LEVEL = 4
        
        # 故障分析
        self.PRE_FAULT_WINDOWS = 10
        self.POST_FAULT_OFFSET = 8
        
        # 图表设置
        self.FIGURE_SIZE = (15, 10)
        self.SAVE_PLOTS = True
        self.DPI = 300
        
        # 文件组织
        self.OUTPUT_DIR = 'results'
        self.PLOTS_DIR = 'results/plots'
        self.DATA_DIR = 'results/data'
        self.REPORTS_DIR = 'results/reports'
        
        # 故障影响量化参数
        self.IMPACT_WEIGHTS = {
            'thd_weight': 0.3,
            'power_weight': 0.25,
            'voltage_weight': 0.2,
            'frequency_weight': 0.15,
            'harmonic_weight': 0.1
        }
        
        self.SEVERITY_THRESHOLDS = {
            'mild': 0.3,      # 轻微影响
            'moderate': 0.6,  # 中等影响
            'severe': 1.0     # 严重影响
        }
        
        # 创建输出目录
        self._create_output_directories()
    
    def _create_output_directories(self) -> None:
        """创建输出文件夹结构"""
        for directory in [self.OUTPUT_DIR, self.PLOTS_DIR, self.DATA_DIR, self.REPORTS_DIR]:
            os.makedirs(directory, exist_ok=True)


class DataLoader:
    """数据加载和预处理类"""
    
    def __init__(self, config: AnalysisConfig):
        """
        初始化数据加载器
        
        Args:
            config: 分析配置对象
        """
        self.config = config
        self.mat_data = None
        self.data_dict = None
        self.sampling_rate = None
        self.time_vector = None
        self.thd_vars = []
    
    def load_data(self) -> Tuple[Optional[Dict], Optional[float], Optional[np.ndarray]]:
        """
        加载和验证数据
        
        Returns:
            Tuple[data_dict, sampling_rate, time_vector]: 数据字典、采样率和时间向量
        """
        try:
            self.mat_data = scipy.io.loadmat(self.config.MAT_FILE_PATH)
            print(f"✅ 文件加载成功: {self.config.MAT_FILE_PATH}")
        except FileNotFoundError:
            print(f"❌ 文件未找到: {self.config.MAT_FILE_PATH}")
            return None, None, None
        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
            return None, None, None
        
        # 探索文件内容
        self._explore_mat_file()
        
        # 提取数据
        if not self._extract_variables():
            return None, None, None
        
        # 计算采样参数
        self._calculate_sampling_parameters()
        
        print(f"📊 数据加载完成:")
        print(f"   - 采样点数: {len(self.data_dict['Ia'])}")
        print(f"   - 采样频率: {self.sampling_rate:.2f} Hz")
        print(f"   - THD变量: {len(self.thd_vars)} 个")
        
        return self.data_dict, self.sampling_rate, self.time_vector
    
    def _explore_mat_file(self) -> None:
        """探索.mat文件中的所有变量，寻找THD相关数据"""
        user_vars = {k: v for k, v in self.mat_data.items() if not k.startswith('__')}
        
        for var_name in user_vars.keys():
            # 检查是否为THD相关变量
            thd_keywords = ['thd', 'harmonic', 'distortion', 'quality']
            if any(keyword in var_name.lower() for keyword in thd_keywords):
                self.thd_vars.append(var_name)
    
    def _extract_variables(self) -> bool:
        """提取必要变量"""
        required_vars = ['Ia', 'Ib', 'Ic', 'Va', 'Vb', 'Vc', 'Vdc']
        optional_vars = ['THD']
        self.data_dict = {}
        
        # 加载必需变量
        for var in required_vars:
            if var in self.mat_data:
                self.data_dict[var] = self.mat_data[var].flatten()
            else:
                print(f"❌ 缺少变量: {var}")
                return False
        
        # 加载可选变量（THD数据）
        for var in optional_vars:
            if var in self.mat_data:
                self.data_dict[var] = self.mat_data[var]
        
        return True
    
    def _calculate_sampling_parameters(self) -> None:
        """计算采样参数"""
        num_samples = len(self.data_dict['Ia'])
        self.sampling_rate = num_samples / self.config.SIM_TIME
        self.time_vector = np.linspace(0, self.config.SIM_TIME, num_samples)
    
    def get_thd_data(self) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray]]:
        """
        获取预计算的THD数据
        
        Returns:
            Tuple[thd_a, thd_b, thd_c]: 三相THD数据
        """
        if not self.thd_vars or not self.data_dict:
            return None, None, None
        
        # 优先选择最可能的THD变量
        priority_names = ['THD', 'thd', 'THD_A', 'ia_thd', 'THD_Ia', 'current_thd']
        
        for name in priority_names:
            if name in self.thd_vars and name in self.data_dict:
                thd_data_raw = self.data_dict[name]
                
                # 检查是否为三相数据
                if len(thd_data_raw.shape) == 2 and thd_data_raw.shape[1] == 3:
                    return (thd_data_raw[:, 0].flatten(), 
                           thd_data_raw[:, 1].flatten(), 
                           thd_data_raw[:, 2].flatten())
                elif len(thd_data_raw.shape) <= 2:
                    return thd_data_raw.flatten(), None, None
        
        # 如果没有找到优先名称，使用第一个THD相关变量
        if self.thd_vars and self.thd_vars[0] in self.data_dict:
            thd_data_raw = self.data_dict[self.thd_vars[0]]
            if len(thd_data_raw.shape) == 2 and thd_data_raw.shape[1] == 3:
                return (thd_data_raw[:, 0].flatten(), 
                       thd_data_raw[:, 1].flatten(), 
                       thd_data_raw[:, 2].flatten())
            else:
                return thd_data_raw.flatten(), None, None
        
        return None, None, None


class FeatureExtractor:
    """特征提取器类"""

    def __init__(self, config: AnalysisConfig):
        """
        初始化特征提取器

        Args:
            config: 分析配置对象
        """
        self.config = config
        self.precomputed_thd_a = None
        self.precomputed_thd_b = None
        self.precomputed_thd_c = None

    def set_thd_data(self, thd_a: Optional[np.ndarray], thd_b: Optional[np.ndarray],
                     thd_c: Optional[np.ndarray]) -> None:
        """设置预计算的THD数据"""
        self.precomputed_thd_a = thd_a
        self.precomputed_thd_b = thd_b
        self.precomputed_thd_c = thd_c

    def extract_basic_features(self, signal_data: np.ndarray) -> Dict[str, float]:
        """
        提取基础时域特征

        Args:
            signal_data: 信号数据

        Returns:
            特征字典
        """
        features = {}

        # 基础统计特征
        features['rms'] = np.sqrt(np.mean(signal_data**2))
        features['peak'] = np.max(np.abs(signal_data))
        features['mean'] = np.mean(signal_data)
        features['std'] = np.std(signal_data)
        features['skewness'] = scipy.stats.skew(signal_data)
        features['kurtosis'] = scipy.stats.kurtosis(signal_data)

        # 波形因子
        if features['rms'] != 0:
            features['crest_factor'] = features['peak'] / features['rms']
        else:
            features['crest_factor'] = 0

        return features

    def extract_thd_at_time(self, thd_data: np.ndarray, time_vector: np.ndarray,
                           target_time: float, phase: str = 'a') -> Optional[float]:
        """
        从预计算THD数据中提取指定时间的THD值

        Args:
            thd_data: THD数据数组
            time_vector: 时间向量
            target_time: 目标时间
            phase: 相别标识

        Returns:
            THD值
        """
        try:
            # 找到最接近目标时间的索引
            time_idx = np.argmin(np.abs(time_vector - target_time))

            # 确保索引在有效范围内
            if thd_data is not None and 0 <= time_idx < len(thd_data):
                return float(thd_data[time_idx])
            else:
                return None
        except Exception as e:
            print(f"⚠️ THD提取错误 (相别: {phase}): {e}")
            return None

    def extract_frequency_features(self, signal_data: np.ndarray, fs: float,
                                 window_center_time: Optional[float] = None,
                                 time_vector: Optional[np.ndarray] = None,
                                 signal_phase: str = 'a') -> Dict[str, float]:
        """
        提取频域特征

        Args:
            signal_data: 信号数据
            fs: 采样频率
            window_center_time: 窗口中心时间
            time_vector: 时间向量
            signal_phase: 信号相别

        Returns:
            频域特征字典
        """
        features = {}
        N = len(signal_data)

        # 使用预计算THD数据
        thd_from_precomputed = False
        current_thd_data = None

        if signal_phase.lower() == 'a' and self.precomputed_thd_a is not None:
            current_thd_data = self.precomputed_thd_a
        elif signal_phase.lower() == 'b' and self.precomputed_thd_b is not None:
            current_thd_data = self.precomputed_thd_b
        elif signal_phase.lower() == 'c' and self.precomputed_thd_c is not None:
            current_thd_data = self.precomputed_thd_c
        elif self.precomputed_thd_a is not None:  # 默认使用A相
            current_thd_data = self.precomputed_thd_a

        if (current_thd_data is not None and window_center_time is not None and
            time_vector is not None):

            thd_value = self.extract_thd_at_time(current_thd_data, time_vector,
                                               window_center_time, signal_phase)

            if thd_value is not None:
                # 根据数值大小判断格式
                if thd_value > 1:  # 百分比形式
                    features['thd_percent'] = thd_value
                    features['thd'] = thd_value / 100
                else:  # 小数形式
                    features['thd'] = thd_value
                    features['thd_percent'] = thd_value * 100

                features['thd_source'] = 'precomputed'
                thd_from_precomputed = True

        # 进行FFT分析获取其他频域特征
        if N % 2 != 0:
            signal_data = signal_data[:-1]
            N = len(signal_data)

        # 应用汉宁窗
        windowed_signal = signal_data * np.hanning(N)

        # FFT分析
        yf = np.fft.fft(windowed_signal)
        yf_mag = 2.0/N * np.abs(yf[:N//2])
        freqs = np.fft.fftfreq(N, 1/fs)[:N//2]

        # 基波分析
        fundamental_freq = self.config.GRID_FREQ
        freq_resolution = fs / N
        search_range = max(1, int(2 / freq_resolution))

        fundamental_idx = np.argmin(np.abs(freqs - fundamental_freq))
        start_idx = max(0, fundamental_idx - search_range)
        end_idx = min(len(yf_mag), fundamental_idx + search_range)

        if end_idx > start_idx:
            local_peak_idx = np.argmax(yf_mag[start_idx:end_idx])
            fundamental_idx = start_idx + local_peak_idx

        features['fundamental_mag'] = yf_mag[fundamental_idx]
        features['fundamental_freq'] = freqs[fundamental_idx]

        # 谐波分析
        harmonics_power = 0
        for h in self.config.HARMONICS:
            harmonic_freq = h * features['fundamental_freq']

            if harmonic_freq >= fs/2:
                continue

            harmonic_idx = np.argmin(np.abs(freqs - harmonic_freq))
            h_search_range = max(1, int(1 / freq_resolution))
            h_start_idx = max(0, harmonic_idx - h_search_range)
            h_end_idx = min(len(yf_mag), harmonic_idx + h_search_range)

            if h_end_idx > h_start_idx:
                local_h_peak_idx = np.argmax(yf_mag[h_start_idx:h_end_idx])
                harmonic_idx = h_start_idx + local_h_peak_idx
                harmonic_mag = yf_mag[harmonic_idx]

                features[f'h{h}_mag'] = harmonic_mag
                features[f'h{h}_freq'] = freqs[harmonic_idx]
                harmonics_power += harmonic_mag**2

        # 如果没有预计算THD，设为0
        if not thd_from_precomputed:
            features['thd'] = 0
            features['thd_percent'] = 0
            features['thd_source'] = 'unavailable'

        # 计算其他频域特征
        if 'fundamental_mag' in features and features['fundamental_mag'] > 0:
            features['fundamental_power'] = features['fundamental_mag']**2
            features['harmonics_total_power'] = harmonics_power
            features['signal_quality'] = features['fundamental_power'] / (
                features['fundamental_power'] + harmonics_power)
        else:
            features['fundamental_power'] = 0
            features['harmonics_total_power'] = 0
            features['signal_quality'] = 0

        return features

    def extract_wavelet_features(self, signal_data: np.ndarray) -> Dict[str, float]:
        """
        提取小波特征

        Args:
            signal_data: 信号数据

        Returns:
            小波特征字典
        """
        features = {}

        if not PYWT_AVAILABLE:
            return features

        try:
            coeffs = pywt.wavedec(signal_data, self.config.WAVELET_TYPE,
                                level=self.config.WAVELET_LEVEL)

            # 各层能量
            for i in range(self.config.WAVELET_LEVEL):
                features[f'wavelet_d{i+1}'] = np.sum(coeffs[i+1]**2)

            features['wavelet_a'] = np.sum(coeffs[0]**2)
        except Exception:
            pass

        return features


class FaultAnalyzer:
    """故障分析器类"""

    def __init__(self, config: AnalysisConfig):
        """
        初始化故障分析器

        Args:
            config: 分析配置对象
        """
        self.config = config
        self.data_loader = DataLoader(config)
        self.feature_extractor = FeatureExtractor(config)

        # 数据存储
        self.data_dict = None
        self.sampling_rate = None
        self.time_vector = None
        self.feature_df = None

    def load_and_prepare_data(self) -> bool:
        """
        加载和准备数据

        Returns:
            是否成功加载数据
        """
        print("📊 开始数据加载和准备...")

        # 加载数据
        self.data_dict, self.sampling_rate, self.time_vector = self.data_loader.load_data()

        if self.data_dict is None:
            print("❌ 数据加载失败")
            return False

        # 获取THD数据
        thd_a, thd_b, thd_c = self.data_loader.get_thd_data()
        self.feature_extractor.set_thd_data(thd_a, thd_b, thd_c)

        if thd_a is not None:
            print(f"✅ THD数据加载成功:")
            print(f"   - A相THD范围: [{np.min(thd_a):.4f}, {np.max(thd_a):.4f}]")
            if thd_b is not None:
                print(f"   - B相THD范围: [{np.min(thd_b):.4f}, {np.max(thd_b):.4f}]")
            if thd_c is not None:
                print(f"   - C相THD范围: [{np.min(thd_c):.4f}, {np.max(thd_c):.4f}]")
        else:
            print("⚠️ 未找到THD数据")

        return True

    def perform_windowed_analysis(self) -> bool:
        """
        执行分窗分析

        Returns:
            是否成功完成分析
        """
        if self.data_dict is None:
            print("❌ 无数据进行分析")
            return False

        print("🔍 开始分窗分析...")

        # 计算窗口参数
        window_size = int(self.sampling_rate * self.config.CYCLES_PER_WINDOW / self.config.GRID_FREQ)
        step_size = int(window_size * (1 - self.config.WINDOW_OVERLAP))
        num_windows = (len(self.data_dict['Ia']) - window_size) // step_size + 1

        print(f"📊 分窗参数:")
        print(f"   - 窗口大小: {window_size} 点 ({window_size/self.sampling_rate*1000:.1f} ms)")
        print(f"   - 步长: {step_size} 点")
        print(f"   - 总窗口数: {num_windows}")

        # 提取特征
        all_features = []

        for i in range(num_windows):
            start_idx = i * step_size
            end_idx = start_idx + window_size
            window_center_time = (start_idx + end_idx) / 2 / self.sampling_rate

            # 当前窗口数据
            window_data = {}
            for key, data in self.data_dict.items():
                window_data[key] = data[start_idx:end_idx]

            # 提取A相电流特征
            features = {'window_time': window_center_time}

            # 时域特征
            time_features = self.feature_extractor.extract_basic_features(window_data['Ia'])
            for key, value in time_features.items():
                features[f'ia_{key}'] = value

            # 频域特征
            freq_features = self.feature_extractor.extract_frequency_features(
                window_data['Ia'],
                self.sampling_rate,
                window_center_time=window_center_time,
                time_vector=self.time_vector,
                signal_phase='a'
            )
            for key, value in freq_features.items():
                features[f'ia_{key}'] = value

            # 小波特征
            wavelet_features = self.feature_extractor.extract_wavelet_features(window_data['Ia'])
            for key, value in wavelet_features.items():
                features[f'ia_{key}'] = value

            # 直流电压特征
            dc_features = self.feature_extractor.extract_basic_features(window_data['Vdc'])
            for key, value in dc_features.items():
                features[f'vdc_{key}'] = value

            # 功率特征
            p_inst = (window_data['Va'] * window_data['Ia'] +
                      window_data['Vb'] * window_data['Ib'] +
                      window_data['Vc'] * window_data['Ic'])
            features['active_power'] = np.mean(p_inst)
            features['power_std'] = np.std(p_inst)

            # 三相不平衡度
            ia_rms = np.sqrt(np.mean(window_data['Ia']**2))
            ib_rms = np.sqrt(np.mean(window_data['Ib']**2))
            ic_rms = np.sqrt(np.mean(window_data['Ic']**2))
            avg_rms = (ia_rms + ib_rms + ic_rms) / 3
            if avg_rms > 0:
                features['current_unbalance'] = np.std([ia_rms, ib_rms, ic_rms]) / avg_rms
            else:
                features['current_unbalance'] = 0

            all_features.append(features)

            if (i + 1) % 50 == 0:
                print(f"   已处理 {i+1}/{num_windows} 个窗口")

        # 转换为DataFrame
        self.feature_df = pd.DataFrame(all_features)

        # 添加故障标签
        self.feature_df['fault_label'] = (self.feature_df['window_time'] >= self.config.FAULT_TIME).astype(int)

        print(f"✅ 特征提取完成!")
        print(f"   - 特征数: {len(self.feature_df.columns)}")
        print(f"   - 数据维度: {self.feature_df.shape}")

        # 保存特征数据
        self.feature_df.to_csv(f'{self.config.DATA_DIR}/features.csv', index=False)
        print(f"✅ 特征数据已保存到: {self.config.DATA_DIR}/features.csv")

        return True

    def calculate_fault_impact(self) -> Optional[pd.DataFrame]:
        """
        计算故障影响

        Returns:
            故障影响分析结果
        """
        if self.feature_df is None:
            print("❌ 无特征数据进行分析")
            return None

        print("📈 计算故障影响...")

        # 分离故障前后数据
        pre_fault_data = self.feature_df[self.feature_df['fault_label'] == 0]
        post_fault_data = self.feature_df[self.feature_df['fault_label'] == 1]

        if len(pre_fault_data) == 0 or len(post_fault_data) == 0:
            print("❌ 故障前后数据不足")
            return None

        # 选择稳态窗口
        pre_fault_stable = pre_fault_data.tail(self.config.PRE_FAULT_WINDOWS)
        post_fault_stable = post_fault_data.iloc[
            self.config.POST_FAULT_OFFSET:self.config.POST_FAULT_OFFSET+10]

        print(f"📊 分析窗口:")
        print(f"   - 故障前稳态: {len(pre_fault_stable)} 个窗口")
        print(f"   - 故障后稳态: {len(post_fault_stable)} 个窗口")

        # 选择数值特征
        numeric_cols = self.feature_df.select_dtypes(include=[np.number]).columns
        exclude_cols = ['window_time', 'fault_label']
        feature_cols = [col for col in numeric_cols if col not in exclude_cols]

        # 计算平均值
        pre_fault_mean = pre_fault_stable[feature_cols].mean()
        post_fault_mean = post_fault_stable[feature_cols].mean()

        # 计算影响向量
        absolute_impact = post_fault_mean - pre_fault_mean
        relative_impact = (post_fault_mean - pre_fault_mean) / (pre_fault_mean + 1e-10) * 100

        # 创建影响分析结果
        impact_analysis = pd.DataFrame({
            'Pre_Fault': pre_fault_mean,
            'Post_Fault': post_fault_mean,
            'Absolute_Change': absolute_impact,
            'Relative_Change_Percent': relative_impact,
            'Impact_Magnitude': np.abs(relative_impact)
        })

        # 按影响程度排序
        impact_analysis = impact_analysis.sort_values('Impact_Magnitude', ascending=False)

        print(f"🎯 故障影响最显著的前10个特征:")
        print(impact_analysis.head(10)[['Relative_Change_Percent', 'Absolute_Change']].round(4))

        # 保存影响分析结果
        impact_analysis.to_csv(f'{self.config.DATA_DIR}/impact_analysis.csv')
        print(f"✅ 影响分析结果已保存到: {self.config.DATA_DIR}/impact_analysis.csv")

        return impact_analysis

    def generate_simple_report(self, impact_analysis: pd.DataFrame) -> None:
        """
        生成简化的分析报告

        Args:
            impact_analysis: 影响分析结果
        """
        print("📋 生成分析报告...")

        if self.feature_df is None or impact_analysis is None:
            print("❌ 无数据生成报告")
            return

        # 计算关键指标
        pre_fault_data = self.feature_df[self.feature_df['fault_label'] == 0]
        post_fault_data = self.feature_df[self.feature_df['fault_label'] == 1]

        # THD变化分析
        if 'ia_thd_percent' in self.feature_df.columns:
            pre_thd = pre_fault_data['ia_thd_percent'].mean()
            post_thd = post_fault_data['ia_thd_percent'].mean()
        else:
            pre_thd = pre_fault_data['ia_thd'].mean() * 100
            post_thd = post_fault_data['ia_thd'].mean() * 100

        thd_change = post_thd - pre_thd
        thd_change_percent = (thd_change / pre_thd) * 100 if pre_thd > 0 else 0

        # 功率变化分析
        pre_power = pre_fault_data['active_power'].mean() / 1000  # 转换为kW
        post_power = post_fault_data['active_power'].mean() / 1000
        power_loss = pre_power - post_power
        power_loss_percent = (power_loss / pre_power) * 100 if pre_power > 0 else 0

        # 生成报告
        report_content = f"""
# 光伏断路故障电能质量影响分析报告

## 系统配置
- 系统容量: {self.config.RATED_POWER/1000:.0f} kW
- 故障时间: {self.config.FAULT_TIME:.1f} 秒
- 分析时长: {self.config.SIM_TIME:.1f} 秒
- 分析窗口数: {len(self.feature_df)}
- 提取特征数: {len(self.feature_df.columns)-2}

## 关键发现

### 总谐波畸变率(THD)变化
- 故障前THD: {pre_thd:.3f}%
- 故障后THD: {post_thd:.3f}%
- THD变化量: {thd_change:.3f}% (相对变化: {thd_change_percent:.1f}%)
- 国标限值: 5.0%
- 是否超标: {'是' if post_thd > 5.0 else '否'}

### 有功功率变化
- 故障前功率: {pre_power:.1f} kW
- 故障后功率: {post_power:.1f} kW
- 功率损失: {power_loss:.1f} kW (损失率: {power_loss_percent:.1f}%)

### 最显著影响特征(前5项)
"""

        for i, (feature, row) in enumerate(impact_analysis.head(5).iterrows(), 1):
            report_content += f"{i}. {feature}: 相对变化 {row['Relative_Change_Percent']:.2f}%\n"

        report_content += f"""
## 结论与建议

### 主要结论
1. PV Array4断路故障对电网电能质量产生了明显影响
2. THD变化是主要的影响因素，需要重点关注
3. 功率损失达到{power_loss:.1f}kW，占总容量的{power_loss/(self.config.RATED_POWER/1000)*100:.1f}%

### 技术建议
1. 实施主动谐波补偿，重点抑制低次谐波
2. 调整剩余阵列功率输出，补偿功率损失
3. 优化直流侧电压控制，减少电压波动
4. 采用MPC预测控制，实现故障的主动补偿

---
报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
分析软件: 光伏故障电能质量影响分析系统 v2.0 (优化版)
"""

        # 保存报告
        report_path = f'{self.config.REPORTS_DIR}/analysis_report.md'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✅ 分析报告已生成: {report_path}")

        # 打印关键摘要
        print(f"\n📊 关键结果摘要:")
        print(f"   - THD变化: {pre_thd:.3f}% → {post_thd:.3f}% (变化{thd_change:+.3f}%)")
        print(f"   - 功率损失: {power_loss:.1f} kW ({power_loss_percent:.1f}%)")
        print(f"   - 分析窗口: {len(self.feature_df)} 个")
        print(f"   - 提取特征: {len(self.feature_df.columns)-2} 个")

    def run_complete_analysis(self) -> bool:
        """
        运行完整的分析流程

        Returns:
            是否成功完成分析
        """
        print("🚀 开始完整的光伏故障分析...")

        # 1. 加载和准备数据
        if not self.load_and_prepare_data():
            return False

        # 2. 执行分窗分析
        if not self.perform_windowed_analysis():
            return False

        # 3. 计算故障影响
        impact_analysis = self.calculate_fault_impact()
        if impact_analysis is None:
            return False

        # 4. 生成报告
        self.generate_simple_report(impact_analysis)

        print("\n🎉 光伏故障分析完成！")
        print(f"📁 所有结果文件已保存到:")
        print(f"   - 数据文件: {self.config.DATA_DIR}/")
        print(f"   - 报告文件: {self.config.REPORTS_DIR}/")

        return True


def main():
    """主程序入口"""
    print("=" * 60)
    print("光伏断路故障电能质量影响分析系统 - 优化版 v2.0")
    print("=" * 60)

    try:
        # 创建配置和分析器
        config = AnalysisConfig()
        analyzer = FaultAnalyzer(config)

        # 运行完整分析
        success = analyzer.run_complete_analysis()

        if success:
            print("\n✅ 分析成功完成！")
        else:
            print("\n❌ 分析过程中出现错误")

    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
